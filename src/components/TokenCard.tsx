'use client';

import { useState } from 'react';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

interface TokenCardProps {
  token: TokenInfo;
  isAnimated?: boolean;
  category: 'new' | 'bonding' | 'bonded';
}

export default function TokenCard({ token, isAnimated = false, category }: TokenCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const formatNumber = (num: number | undefined, decimals = 2): string => {
    if (!num || num === 0) return '0';
    
    if (num >= 1e9) return `${(num / 1e9).toFixed(decimals)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(decimals)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(decimals)}K`;
    
    return num.toFixed(decimals);
  };

  const formatPrice = (price: number | undefined): string => {
    if (!price || price === 0) return '$0.00';
    
    if (price < 0.01) {
      return `$${price.toExponential(2)}`;
    }
    
    return `$${price.toFixed(price < 1 ? 4 : 2)}`;
  };

  const getTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const getCategoryBadge = () => {
    switch (category) {
      case 'new':
        return (
          <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium">
            New
          </div>
        );
      case 'bonding':
        return (
          <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full text-xs font-medium">
            Bonding
          </div>
        );
      case 'bonded':
        return (
          <div className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium">
            Bonded
          </div>
        );
    }
  };

  const handleSocialClick = (url: string | undefined, e: React.MouseEvent) => {
    e.stopPropagation();
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div 
      className={`token-card bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-all duration-300 cursor-pointer ${
        isAnimated ? 'animate-fade-in-up' : ''
      }`}
      onClick={() => setShowDetails(!showDetails)}
    >
      {/* Main Card Content */}
      <div className="flex items-start gap-3">
        {/* Token Image */}
        <div className="relative flex-shrink-0">
          {token.image && !imageError ? (
            <img
              src={token.image}
              alt={token.name}
              className={`w-12 h-12 rounded-full object-cover transition-opacity duration-300 ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setImageLoaded(true)}
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
              <span className="text-sm font-bold text-white">
                {token.symbol.slice(0, 2).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        {/* Token Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="min-w-0 flex-1">
              <h4 className="font-semibold text-gray-900 dark:text-white truncate">
                {token.name}
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                ${token.symbol}
              </p>
            </div>
            <div className="flex items-center gap-2 ml-2">
              {getCategoryBadge()}
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Market Cap:</span>
              <div className="font-medium text-gray-900 dark:text-white">
                {formatPrice(token.marketCap)}
              </div>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Price:</span>
              <div className="font-medium text-gray-900 dark:text-white">
                {formatPrice(token.priceUSD)}
              </div>
            </div>
          </div>

          {/* Time and Social Links */}
          <div className="flex items-center justify-between mt-3">
            <span className="text-xs text-gray-400">
              {getTimeAgo(token.timestamp)}
            </span>
            
            {/* Social Links */}
            <div className="flex items-center gap-1">
              {token.twitter && (
                <button
                  onClick={(e) => handleSocialClick(token.twitter, e)}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                  title="Twitter"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </button>
              )}
              {token.telegram && (
                <button
                  onClick={(e) => handleSocialClick(token.telegram, e)}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                  title="Telegram"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                  </svg>
                </button>
              )}
              {token.website && (
                <button
                  onClick={(e) => handleSocialClick(token.website, e)}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                  title="Website"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 animate-fade-in">
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-500 dark:text-gray-400 block">Liquidity (SOL):</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {formatNumber(token.liquiditySOL)} SOL
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400 block">24h Volume:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {formatNumber(token.volume24h)} SOL
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400 block">Total Volume:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {formatNumber(token.volumeTotal)} SOL
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400 block">Transactions:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {formatNumber(token.transactionCount, 0)}
              </span>
            </div>
          </div>
          
          {token.description && (
            <div className="mt-3">
              <span className="text-gray-500 dark:text-gray-400 text-xs block mb-1">Description:</span>
              <p className="text-xs text-gray-700 dark:text-gray-300 line-clamp-2">
                {token.description}
              </p>
            </div>
          )}
          
          <div className="mt-3 text-xs text-gray-400 font-mono">
            {token.mint.slice(0, 8)}...{token.mint.slice(-8)}
          </div>
        </div>
      )}
    </div>
  );
}
